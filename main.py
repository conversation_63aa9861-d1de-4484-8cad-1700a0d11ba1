import streamlit as st
import requests
import openai
import os
import sqlite3
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# OpenAI API setup
openai.api_key = os.getenv("OPENAI_API_KEY")

# Database Setup (SQLite)
conn = sqlite3.connect('products.db')
c = conn.cursor()

# Create a table to store products
def create_table():
    c.execute('''CREATE TABLE IF NOT EXISTS products
                 (id INTEGER PRIMARY KEY, name TEXT, link TEXT, price TEXT)''')

# Function to add a product to the database
def add_product(name, link, price):
    c.execute("INSERT INTO products (name, link, price) VALUES (?, ?, ?)", (name, link, price))
    conn.commit()

# Function to remove a product
def remove_product(product_id):
    c.execute("DELETE FROM products WHERE id=?", (product_id,))
    conn.commit()

# Fetch all products from the database
def get_products():
    c.execute("SELECT * FROM products")
    return c.fetchall()

# Amazon Affiliate API (dummy example, replace with actual Amazon API logic)
def fetch_amazon_product(keyword):
    # In real scenario, use Amazon API to fetch product details
    return {
        'name': f'{keyword} Product Example',
        'link': 'https://www.amazon.com/example-product',
        'price': '$29.99'
    }

# Generate blog post using OpenAI GPT
def generate_blog_post(product_name, product_link, product_price):
    prompt = f"Write a blog post about the product '{product_name}', priced at {product_price}, available at {product_link}."
    response = openai.Completion.create(
        engine="text-davinci-003",
        prompt=prompt,
        max_tokens=300
    )
    return response.choices[0].text

# Streamlit UI Layout
st.title("Amazon Affiliate Product Manager & Blog Generator")

# Section 1: Add Products
st.header("Add a Product")
keyword = st.text_input("Enter Product Keyword")
if st.button("Fetch Product from Amazon"):
    product = fetch_amazon_product(keyword)
    st.write("Product Found:")
    st.write(f"**Name:** {product['name']}")
    st.write(f"**Price:** {product['price']}")
    st.write(f"**Link:** {product['link']}")
    if st.button("Add Product to Database"):
        add_product(product['name'], product['link'], product['price'])
        st.success(f"Product '{product['name']}' added!")

# Section 2: Manage Products
st.header("Manage Products")
products = get_products()
for product in products:
    st.write(f"**{product[1]}** - {product[3]} [Link]({product[2]})")
    if st.button(f"Remove {product[1]}", key=product[0]):
        remove_product(product[0])
        st.success(f"Product '{product[1]}' removed!")

# Section 3: Generate Blog Posts
st.header("Generate Blog Post for a Product")
product_list = [f"{product[1]} ({product[3]})" for product in products]
selected_product = st.selectbox("Select a product", product_list)

if selected_product:
    product_index = product_list.index(selected_product)
    selected_product_details = products[product_index]
    
    if st.button(f"Generate Blog Post for {selected_product}"):
        blog_content = generate_blog_post(selected_product_details[1], selected_product_details[2], selected_product_details[3])
        st.write("### Generated Blog Post")
        st.write(blog_content)
